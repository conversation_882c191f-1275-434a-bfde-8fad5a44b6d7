/**
 * <PERSON><PERSON><PERSON>ey Advanced Multi-Agent Orchestrator
 * 
 * This is the most powerful multi-agent orchestration system built on LangGraph.js
 * Features:
 * - Dynamic workflow generation based on detected roles
 * - Supervisor patterns for complex coordination
 * - Parallel execution for efficiency
 * - Memory persistence across conversations
 * - Tool calling and function execution
 * - Real-time streaming responses
 * - Error handling and recovery
 * - Comprehensive logging
 */

import { StateGraph, START, END } from '@langchain/langgraph';
import { BaseMessage, HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';
import { RunnableConfig } from '@langchain/core/runnables';
import { createReactAgent } from '@langchain/langgraph/prebuilt';
import { RouKeyLLM } from './RouKeyLLM';
import { type ApiKey } from '@/types/apiKeys';
import { PREDEFINED_ROLES } from '@/config/roles';
import { type ProgressCallback } from './RouKeyLangGraphIntegration';

// State interface for the orchestration
interface OrchestrationState {
  messages: BaseMessage[];
  next?: string;
  currentRole?: string;
  taskProgress?: Record<string, any>;
  results?: Record<string, string>;
  metadata?: Record<string, any>;
  conversationId?: string;
  userId?: string;
}

// Configuration for orchestration
export interface OrchestrationConfig {
  roles: string[];
  userApiKeys: Record<string, ApiKey>;
  originalPrompt: string;
  conversationId?: string;
  userId?: string;
  customApiConfigId?: string; // Required for provider requests
  workflowType?: 'sequential' | 'parallel' | 'supervisor' | 'hierarchical';
  maxIterations?: number;
  enableMemory?: boolean;
}

// Result interface
export interface OrchestrationResult {
  success: boolean;
  finalResponse: string;
  roleContributions: Record<string, string>;
  metadata: {
    totalTokens: number;
    executionTime: number;
    workflowType: string;
    rolesUsed: string[];
  };
  conversationHistory: BaseMessage[];
  userApiKeys?: Record<string, ApiKey>; // For logging purposes
}

export class RouKeyOrchestrator {
  private config: OrchestrationConfig;
  private agents: Record<string, any> = {};
  private workflow: any = null;
  private progressCallback?: ProgressCallback;

  constructor(config: OrchestrationConfig, progressCallback?: ProgressCallback) {
    this.config = config;
    this.progressCallback = progressCallback;
    console.log(`[RouKey Orchestrator] 🚀 Initializing with roles: ${config.roles.join(', ')}`);
  }

  /**
   * Initialize agents for each role using RouKey's BYOK system
   */
  private async initializeAgents(): Promise<void> {
    console.log(`[RouKey Orchestrator] 🔧 Creating agents for ${this.config.roles.length} roles`);

    for (const role of this.config.roles) {
      const apiKey = this.config.userApiKeys[role];
      if (!apiKey) {
        console.warn(`[RouKey Orchestrator] ⚠️ No API key found for role: ${role}`);
        continue;
      }

      // Create RouKey LLM for this role
      const llm = new RouKeyLLM({
        apiKey,
        temperature: this.getTemperatureForRole(role),
        maxTokens: this.getMaxTokensForRole(role),
        customApiConfigId: this.config.customApiConfigId
      });

      // Debug logging for multi-role orchestration
      console.log(`[RouKey Orchestrator] ✅ Created agent for ${role} using ${apiKey.provider} (customApiConfigId: ${this.config.customApiConfigId || 'MISSING!'})`);
      if (!this.config.customApiConfigId) {
        console.error(`[RouKey Orchestrator] ❌ CRITICAL: customApiConfigId is missing from config! This will cause provider failures.`);
      }

      // Get role-specific system prompt
      const systemPrompt = this.getSystemPromptForRole(role);

      // Create agent with tools (if any)
      const tools = this.getToolsForRole(role);

      const agent = createReactAgent({
        llm,
        tools,
        messageModifier: new SystemMessage(systemPrompt)
      });

      this.agents[role] = {
        agent,
        llm,
        role,
        systemPrompt,
        tools: tools.length
      };


    }
  }

  /**
   * Get system prompt for a specific role
   */
  private getSystemPromptForRole(role: string): string {
    const roleConfig = PREDEFINED_ROLES.find(r => r.id === role);
    const basePrompt = roleConfig?.description || `You are an expert in ${role}.`;

    const enhancedPrompts: Record<string, string> = {
      'brainstorming_ideation': `${basePrompt}

You are a creative brainstorming expert. Your role in this multi-agent collaboration is to:
- Generate innovative and diverse ideas
- Think outside the box and explore unconventional approaches
- Build upon ideas from other agents
- Ask thought-provoking questions to stimulate creativity
- Provide multiple perspectives and alternatives

Always be enthusiastic, creative, and collaborative. When other agents share their work, build upon it creatively.`,

      'writing': `${basePrompt}

You are a professional writing expert. Your role in this multi-agent collaboration is to:
- Transform ideas into well-structured, engaging content
- Adapt writing style to the target audience and purpose
- Ensure clarity, coherence, and compelling narrative flow
- Collaborate with other agents to refine and improve content
- Provide constructive feedback on written materials

Focus on creating high-quality, polished content that achieves the intended goals.`,

      'coding_backend': `${basePrompt}

You are a backend development expert. Your role in this multi-agent collaboration is to:
- Design robust, scalable backend architectures
- Write clean, efficient, and maintainable code
- Consider security, performance, and best practices
- Collaborate with frontend developers and other team members
- Provide technical guidance and code reviews

Always prioritize code quality, security, and scalability in your solutions.`,

      'coding_frontend': `${basePrompt}

You are a frontend development expert. Your role in this multi-agent collaboration is to:
- Create intuitive, responsive user interfaces
- Implement modern frontend technologies and best practices
- Ensure excellent user experience and accessibility
- Collaborate with backend developers and designers
- Optimize for performance and cross-browser compatibility

Focus on creating beautiful, functional, and user-friendly interfaces.`,

      'general_chat': `You are a helpful AI assistant participating in a multi-agent collaboration. Your role is to:
- Provide general assistance and coordination
- Help bridge communication between specialized agents
- Offer balanced perspectives and common-sense insights
- Facilitate smooth collaboration between team members
- Ensure all participants stay focused on the main objectives

Be supportive, clear, and help maintain productive collaboration.`
    };

    return enhancedPrompts[role] || `${basePrompt}

You are participating in a multi-agent collaboration. Work together with other agents to achieve the best possible outcome. Be collaborative, constructive, and focused on the shared goals.`;
  }

  /**
   * Get tools for a specific role
   */
  private getToolsForRole(role: string): any[] {
    // For now, return empty array - tools can be added later
    // TODO: Implement role-specific tools (web search, code execution, etc.)
    return [];
  }

  /**
   * Get temperature setting for role
   */
  private getTemperatureForRole(role: string): number {
    const temperatureMap: Record<string, number> = {
      'brainstorming_ideation': 0.9,
      'writing': 0.7,
      'coding_backend': 0.6, // Increased from 0.3 to help with orchestration context
      'coding_frontend': 0.6, // Increased from 0.3 to help with orchestration context
      'translation_localization': 0.5, // Increased from 0.3
      'summarization_briefing': 0.5,
      'general_chat': 0.7
    };
    return temperatureMap[role] || 0.7;
  }

  /**
   * Get max tokens for role
   */
  private getMaxTokensForRole(role: string): number {
    const tokenMap: Record<string, number> = {
      'brainstorming_ideation': 3000,
      'writing': 4000,
      'coding_backend': 3000,
      'coding_frontend': 3000,
      'translation_localization': 2000,
      'summarization_briefing': 2000,
      'general_chat': 2000
    };
    return tokenMap[role] || 2000;
  }

  /**
   * Determine the best workflow type based on roles and task
   */
  private determineWorkflowType(): 'sequential' | 'parallel' | 'supervisor' | 'hierarchical' {
    const roleCount = this.config.roles.length;
    
    // If workflow type is explicitly set, use it
    if (this.config.workflowType) {
      return this.config.workflowType;
    }

    // Auto-determine based on roles and complexity
    if (roleCount === 2) {
      return 'sequential';
    } else if (roleCount <= 3) {
      return 'supervisor';
    } else {
      return 'hierarchical';
    }
  }

  /**
   * Build workflow based on the determined type
   */
  private async buildWorkflow(workflowType: string): Promise<void> {
    console.log(`[RouKey Orchestrator] 🏗️ Building ${workflowType} workflow`);

    switch (workflowType) {
      case 'sequential':
        this.workflow = this.buildSequentialWorkflow();
        break;
      case 'parallel':
        this.workflow = this.buildParallelWorkflow();
        break;
      case 'supervisor':
        this.workflow = this.buildSupervisorWorkflow();
        break;
      case 'hierarchical':
        this.workflow = this.buildHierarchicalWorkflow();
        break;
      default:
        throw new Error(`Unknown workflow type: ${workflowType}`);
    }
  }

  /**
   * Build sequential workflow - agents work one after another
   */
  private buildSequentialWorkflow() {
    const workflow = new StateGraph<OrchestrationState>({
      channels: {
        messages: {
          value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
          default: () => []
        },
        next: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => undefined
        },
        currentRole: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => undefined
        },
        taskProgress: {
          value: (x: Record<string, any> | undefined, y: Record<string, any> | undefined) => ({ ...(x || {}), ...(y || {}) }),
          default: () => ({})
        },
        results: {
          value: (x: Record<string, any> | undefined, y: Record<string, any> | undefined) => ({ ...(x || {}), ...(y || {}) }),
          default: () => ({})
        },
        metadata: {
          value: (x: Record<string, any> | undefined, y: Record<string, any> | undefined) => ({ ...(x || {}), ...(y || {}) }),
          default: () => ({})
        },
        conversationId: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => this.config.conversationId
        },
        userId: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => this.config.userId
        }
      }
    });

    // Add agent nodes
    this.config.roles.forEach((role, index) => {
      workflow.addNode(role, this.createAgentNode(role));
    });

    // Add sequential edges
    workflow.addEdge(START, this.config.roles[0] as any);
    for (let i = 0; i < this.config.roles.length - 1; i++) {
      workflow.addEdge(this.config.roles[i] as any, this.config.roles[i + 1] as any);
    }
    workflow.addEdge(this.config.roles[this.config.roles.length - 1] as any, END);

    return workflow.compile();
  }

  /**
   * Build parallel workflow - agents work simultaneously
   */
  private buildParallelWorkflow() {
    const workflow = new StateGraph<OrchestrationState>({
      channels: {
        messages: {
          value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
          default: () => []
        },
        next: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => undefined
        },
        currentRole: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => undefined
        },
        taskProgress: {
          value: (x: Record<string, any> | undefined, y: Record<string, any> | undefined) => ({ ...(x || {}), ...(y || {}) }),
          default: () => ({})
        },
        results: {
          value: (x: Record<string, any> | undefined, y: Record<string, any> | undefined) => ({ ...(x || {}), ...(y || {}) }),
          default: () => ({})
        },
        metadata: {
          value: (x: Record<string, any> | undefined, y: Record<string, any> | undefined) => ({ ...(x || {}), ...(y || {}) }),
          default: () => ({})
        },
        conversationId: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => this.config.conversationId
        },
        userId: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => this.config.userId
        }
      }
    });

    // Add agent nodes
    this.config.roles.forEach(role => {
      workflow.addNode(role, this.createAgentNode(role));
    });

    // Add merger node
    workflow.addNode('merger', this.createMergerNode());

    // Connect all agents to start and merger
    this.config.roles.forEach(role => {
      workflow.addEdge(START, role as any);
      workflow.addEdge(role as any, 'merger' as any);
    });
    workflow.addEdge('merger' as any, END);

    return workflow.compile();
  }

  /**
   * Build supervisor workflow - one agent coordinates others
   */
  private buildSupervisorWorkflow() {
    const workflow = new StateGraph<OrchestrationState>({
      channels: {
        messages: {
          value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
          default: () => []
        },
        next: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => undefined
        },
        currentRole: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => undefined
        },
        taskProgress: {
          value: (x: Record<string, any> | undefined, y: Record<string, any> | undefined) => ({ ...(x || {}), ...(y || {}) }),
          default: () => ({})
        },
        results: {
          value: (x: Record<string, any> | undefined, y: Record<string, any> | undefined) => ({ ...(x || {}), ...(y || {}) }),
          default: () => ({})
        },
        metadata: {
          value: (x: Record<string, any> | undefined, y: Record<string, any> | undefined) => ({ ...(x || {}), ...(y || {}) }),
          default: () => ({})
        },
        conversationId: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => this.config.conversationId
        },
        userId: {
          value: (x: string | undefined, y: string | undefined) => y ?? x,
          default: () => this.config.userId
        }
      }
    });

    // Create supervisor agent
    const supervisorRole = this.determineSupervisorRole();
    workflow.addNode('supervisor', this.createSupervisorNode(supervisorRole));

    // Add worker agents - all roles are workers when using dedicated supervisor
    const workerRoles = supervisorRole === 'supervisor' ? this.config.roles : this.config.roles.filter(role => role !== supervisorRole);
    workerRoles.forEach(role => {
      workflow.addNode(role, this.createAgentNode(role));
      workflow.addEdge(role as any, 'supervisor' as any); // Workers report back to supervisor
    });

    // Supervisor decides next action
    workflow.addConditionalEdges(
      'supervisor' as any,
      this.createSupervisorRouter(workerRoles)
    );

    workflow.addEdge(START, 'supervisor' as any);

    return workflow.compile();
  }

  /**
   * Build hierarchical workflow - multiple levels of coordination
   */
  private buildHierarchicalWorkflow() {
    // For now, fall back to supervisor workflow
    // TODO: Implement true hierarchical patterns for complex scenarios
    return this.buildSupervisorWorkflow();
  }

  /**
   * Execute the orchestration
   */
  async execute(): Promise<OrchestrationResult> {
    const startTime = Date.now();
    console.log(`[RouKey Orchestrator] 🎯 Starting orchestration for: "${this.config.originalPrompt.substring(0, 100)}..."`);

    try {
      // Initialize agents
      await this.initializeAgents();

      // Determine workflow type
      const workflowType = this.determineWorkflowType();
      console.log(`[RouKey Orchestrator] 📋 Using ${workflowType} workflow pattern`);

      // Build workflow
      await this.buildWorkflow(workflowType);

      // Execute workflow
      const result = await this.executeWorkflow();

      const executionTime = Date.now() - startTime;
      console.log(`[RouKey Orchestrator] ✅ Orchestration completed in ${executionTime}ms`);

      return {
        success: true,
        finalResponse: result.finalResponse,
        roleContributions: result.roleContributions,
        metadata: {
          totalTokens: result.totalTokens,
          executionTime,
          workflowType,
          rolesUsed: this.config.roles
        },
        conversationHistory: result.conversationHistory
      };

    } catch (error) {
      console.error(`[RouKey Orchestrator] ❌ Orchestration failed:`, error);

      return {
        success: false,
        finalResponse: `Orchestration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        roleContributions: {},
        metadata: {
          totalTokens: 0,
          executionTime: Date.now() - startTime,
          workflowType: 'failed',
          rolesUsed: this.config.roles
        },
        conversationHistory: []
      };
    }
  }

  /**
   * Create an agent node for the workflow
   */
  private createAgentNode(role: string) {
    return async (state: OrchestrationState, config?: RunnableConfig) => {
      console.log(`[RouKey Orchestrator] 🤖 ${role} agent starting work`);

      // Notify progress callback
      const taskDescription = this.getTaskDescriptionForRole(role, state);
      this.progressCallback?.onAgentWorkStart?.(role, taskDescription);

      const agent = this.agents[role];
      if (!agent) {
        throw new Error(`Agent not found for role: ${role}`);
      }

      try {
        // Add role context to the latest message
        const messages = [...state.messages];
        if (messages.length > 0) {
          const lastMessage = messages[messages.length - 1];
          if (lastMessage instanceof HumanMessage) {
            // Get previous work but limit length to avoid overwhelming the prompt
            const previousWork = Object.entries(state.results || {})
              .filter(([r]) => r !== role && r !== 'supervisor' && r !== 'final')
              .map(([r, result]) => {
                // Limit each previous result to 500 chars to keep context manageable
                const truncatedResult = typeof result === 'string' && result.length > 500
                  ? result.substring(0, 500) + '...[truncated]'
                  : result;
                return `${r}: ${truncatedResult}`;
              });

            // Add context about the multi-agent collaboration
            const contextualPrompt = `${lastMessage.content}

[Multi-Agent Context]
You are working as part of a team with the following roles: ${this.config.roles.join(', ')}
Your specific role is: ${role}
Previous work from other agents: ${previousWork.length > 0 ? previousWork.join('\n\n') : 'None yet'}

Please contribute your expertise while building upon any previous work.`;

            console.log(`[RouKey Orchestrator] 🔍 ${role} contextual prompt length: ${contextualPrompt.length} chars`);
            messages[messages.length - 1] = new HumanMessage(contextualPrompt);
          }
        }

        // Execute the agent
        console.log(`[RouKey Orchestrator] 🔍 ${role} invoking agent with ${messages.length} messages`);
        const result = await agent.agent.invoke({ messages }, config);

        console.log(`[RouKey Orchestrator] 🔍 ${role} agent result:`, {
          messageCount: result.messages?.length || 0,
          lastMessageType: result.messages?.[result.messages.length - 1]?.constructor?.name || 'unknown'
        });

        const lastMessage = result.messages[result.messages.length - 1];
        const agentResponse = lastMessage.content;

        console.log(`[RouKey Orchestrator] ✅ ${role} completed work (${agentResponse.length} chars)`);

        // Comprehensive validation for empty responses
        if (!agentResponse || agentResponse.length === 0) {
          console.error(`[RouKey Orchestrator] ❌ ${role} returned empty response!`);
          console.error(`[RouKey Orchestrator] 🔍 Last message:`, lastMessage);
          console.error(`[RouKey Orchestrator] 🔍 Full result:`, result);

          // This should never happen with our improved RouKeyLLM, but if it does, throw an error
          throw new Error(`Agent ${role} returned empty response. This indicates a critical issue with the LLM provider response.`);
        }

        // Additional validation for minimal content
        if (agentResponse.length < 10) {
          console.warn(`[RouKey Orchestrator] ⚠️ ${role} returned very short response (${agentResponse.length} chars): "${agentResponse}"`);
        }

        // Notify progress callback
        this.progressCallback?.onAgentWorkComplete?.(role, agentResponse);

        return {
          messages: [new AIMessage({ content: agentResponse, name: role })],
          currentRole: role,
          results: {
            ...state.results,
            [role]: agentResponse
          },
          taskProgress: {
            ...state.taskProgress,
            [role]: 'completed'
          }
        };

      } catch (error) {
        console.error(`[RouKey Orchestrator] ❌ ${role} agent failed:`, error);

        // For commercial app: Try fallback strategies before giving up
        try {
          console.log(`[RouKey Orchestrator] 🔄 Attempting fallback for ${role}...`);

          // Strategy 1: Try with simplified prompt (remove multi-agent context)
          const simplifiedMessages = [new HumanMessage(this.config.originalPrompt)];
          const fallbackResult = await agent.agent.invoke({ messages: simplifiedMessages }, config);
          const fallbackResponse = fallbackResult.messages[fallbackResult.messages.length - 1].content;

          if (fallbackResponse && fallbackResponse.length > 0) {
            console.log(`[RouKey Orchestrator] ✅ ${role} fallback succeeded (${fallbackResponse.length} chars)`);

            return {
              messages: [new AIMessage({ content: fallbackResponse, name: role })],
              currentRole: role,
              results: {
                ...state.results,
                [role]: fallbackResponse
              },
              taskProgress: {
                ...state.taskProgress,
                [role]: 'completed_fallback'
              }
            };
          }
        } catch (fallbackError) {
          console.error(`[RouKey Orchestrator] ❌ ${role} fallback also failed:`, fallbackError);
        }

        // Strategy 2: Generate role-appropriate response using supervisor's LLM
        try {
          console.log(`[RouKey Orchestrator] 🔄 Attempting supervisor fallback for ${role}...`);

          const supervisorAgent = this.agents[this.config.roles[0]]; // Use first role's LLM
          const rolePrompt = this.createRoleSpecificFallbackPrompt(role, this.config.originalPrompt);

          const supervisorFallbackResult = await supervisorAgent.agent.invoke({
            messages: [new HumanMessage(rolePrompt)]
          }, config);

          const supervisorFallbackResponse = supervisorFallbackResult.messages[supervisorFallbackResult.messages.length - 1].content;

          if (supervisorFallbackResponse && supervisorFallbackResponse.length > 0) {
            console.log(`[RouKey Orchestrator] ✅ ${role} supervisor fallback succeeded (${supervisorFallbackResponse.length} chars)`);

            return {
              messages: [new AIMessage({ content: supervisorFallbackResponse, name: role })],
              currentRole: role,
              results: {
                ...state.results,
                [role]: supervisorFallbackResponse
              },
              taskProgress: {
                ...state.taskProgress,
                [role]: 'completed_supervisor_fallback'
              }
            };
          }
        } catch (supervisorFallbackError) {
          console.error(`[RouKey Orchestrator] ❌ ${role} supervisor fallback failed:`, supervisorFallbackError);
        }

        // Final fallback: Return error but continue workflow
        console.error(`[RouKey Orchestrator] ❌ All fallback strategies failed for ${role}`);
        return {
          messages: [new AIMessage({
            content: `Error in ${role}: ${error instanceof Error ? error.message : 'Unknown error'}`,
            name: role
          })],
          currentRole: role,
          results: {
            ...state.results,
            [role]: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
          },
          taskProgress: {
            ...state.taskProgress,
            [role]: 'failed'
          }
        };
      }
    };
  }

  /**
   * Get task description for a role
   */
  private getTaskDescriptionForRole(role: string, state: OrchestrationState): string {
    const roleDescriptions: Record<string, string> = {
      'brainstorming_ideation': 'Generating creative ideas and innovative approaches',
      'writing': 'Crafting well-structured and engaging content',
      'coding_backend': 'Developing robust backend architecture and code',
      'coding_frontend': 'Creating intuitive user interfaces and experiences',
      'translation_localization': 'Translating and localizing content',
      'summarization_briefing': 'Summarizing and briefing key information',
      'general_chat': 'Providing general assistance and coordination'
    };

    const previousWork = Object.keys(state.results || {}).length;
    const baseDescription = roleDescriptions[role] || `Working on ${role} tasks`;

    if (previousWork > 0) {
      return `${baseDescription} (building on ${previousWork} previous contributions)`;
    }

    return baseDescription;
  }

  /**
   * Create simplified prompt for role (used in fallback scenarios)
   */
  private createSimplifiedPromptForRole(role: string, originalPrompt: string, state: OrchestrationState): string {
    const roleDescription = this.getTaskDescriptionForRole(role, state);

    return `You are an expert in ${roleDescription.toLowerCase()}.

Please provide a comprehensive response to this request: "${originalPrompt}"

Focus on delivering high-quality output that addresses the core requirements. Be thorough and professional in your response.`;
  }

  /**
   * Create role-specific fallback prompt for when an agent fails
   */
  private createRoleSpecificFallbackPrompt(role: string, originalPrompt: string): string {
    const rolePrompts: Record<string, string> = {
      'brainstorming_ideation': `As a creative brainstorming expert, generate innovative ideas for this request: "${originalPrompt}"

Focus on:
- Creative concepts and possibilities
- Innovative approaches and solutions
- Multiple perspectives and angles
- Detailed idea development

Provide comprehensive brainstorming output.`,

      'coding_backend': `As a backend development expert, provide code and technical solutions for this request: "${originalPrompt}"

Focus on:
- Clean, functional code
- Best practices and architecture
- Security and performance considerations
- Complete implementation details

Provide working code with explanations.`,

      'coding_frontend': `As a frontend development expert, create user interface solutions for this request: "${originalPrompt}"

Focus on:
- User-friendly interfaces
- Modern web technologies
- Responsive design principles
- Complete implementation

Provide working frontend code with explanations.`,

      'writing': `As a professional writer, create well-structured content for this request: "${originalPrompt}"

Focus on:
- Clear, engaging writing
- Proper structure and flow
- Target audience considerations
- Polished, professional output

Provide comprehensive written content.`,

      'translation_localization': `As a translation expert, handle the language aspects of this request: "${originalPrompt}"

Focus on:
- Accurate translations
- Cultural considerations
- Localization best practices
- Clear communication

Provide complete translation/localization output.`,

      'summarization_briefing': `As a summarization expert, create concise summaries for this request: "${originalPrompt}"

Focus on:
- Key points and highlights
- Clear, structured summaries
- Essential information extraction
- Professional briefing format

Provide comprehensive summaries.`,

      'general_chat': `As a helpful assistant, provide comprehensive assistance for this request: "${originalPrompt}"

Focus on:
- Clear, helpful responses
- Comprehensive coverage
- Professional communication
- Practical solutions

Provide detailed assistance.`
    };

    return rolePrompts[role] || `As an expert in ${role}, provide comprehensive assistance for this request: "${originalPrompt}"

Focus on your specialized expertise and provide detailed, helpful output.`;
  }

  /**
   * Create a merger node for parallel workflows
   */
  private createMergerNode() {
    return async (state: OrchestrationState, config?: RunnableConfig) => {
      console.log(`[RouKey Orchestrator] 🔄 Merging results from ${Object.keys(state.results || {}).length} agents`);

      const results = state.results || {};
      console.log(`[RouKey Orchestrator] 📋 Available results:`, Object.keys(results));

      const roleContributions = Object.entries(results)
        .map(([role, result]) => `**${role.toUpperCase()}:**\n${result}`)
        .join('\n\n---\n\n');

      const finalResponse = `# Multi-Agent Collaboration Results

${roleContributions}

---

**Summary:** This response was collaboratively created by ${this.config.roles.length} specialized agents, each contributing their expertise to provide you with a comprehensive and well-rounded answer.`;

      console.log(`[RouKey Orchestrator] 📝 Final merged response length: ${finalResponse.length} chars`);
      console.log(`[RouKey Orchestrator] 📝 Final response preview: ${finalResponse.substring(0, 200)}...`);

      return {
        messages: [new AIMessage({ content: finalResponse, name: 'merger' })],
        currentRole: 'merger',
        results: {
          ...results,
          final: finalResponse
        }
      };
    };
  }

  /**
   * Determine which role should be the supervisor
   */
  private determineSupervisorRole(): string {
    // Prefer general_chat as supervisor if available
    if (this.config.roles.includes('general_chat')) {
      return 'general_chat';
    }

    // Create a dedicated supervisor role instead of using one of the worker roles
    // This ensures all specialized roles can do their actual work
    return 'supervisor';
  }

  /**
   * Create supervisor node
   */
  private createSupervisorNode(supervisorRole: string) {
    return async (state: OrchestrationState, config?: RunnableConfig) => {
      console.log(`[RouKey Orchestrator] 👑 Supervisor (${supervisorRole}) coordinating work`);

      // For dedicated supervisor, use the first available agent's LLM
      let agent;
      if (supervisorRole === 'supervisor') {
        // Use the first role's agent LLM for supervisor coordination
        const firstRole = this.config.roles[0];
        agent = this.agents[firstRole];
        if (!agent) {
          throw new Error(`No agent found for supervisor coordination (tried role: ${firstRole})`);
        }
      } else {
        agent = this.agents[supervisorRole];
        if (!agent) {
          throw new Error(`Supervisor agent not found for role: ${supervisorRole}`);
        }
      }

      // For dedicated supervisor, all roles are workers; otherwise exclude supervisor role
      const workerRoles = supervisorRole === 'supervisor' ? this.config.roles : this.config.roles.filter(role => role !== supervisorRole);
      const completedWork = Object.keys(state.results || {}).filter(role => role !== supervisorRole && role !== 'final');
      const remainingWork = workerRoles.filter(role => !completedWork.includes(role));

      let supervisorPrompt: string;

      if (remainingWork.length === 0) {
        // All work is done, create final summary
        this.progressCallback?.onSupervisorSynthesisStart?.();

        supervisorPrompt = `All team members have completed their work. Please create a comprehensive final response that synthesizes all contributions:

${Object.entries(state.results || {})
  .filter(([role]) => role !== supervisorRole)
  .map(([role, result]) => `**${role}:** ${result}`)
  .join('\n\n')}

Create a polished, cohesive final response that incorporates the best insights from each team member.`;
      } else {
        // Decide which agent should work next
        supervisorPrompt = `You are coordinating a multi-agent team. Here's the current status:

Original request: ${this.config.originalPrompt}

Available team members: ${workerRoles.join(', ')}
Completed work: ${completedWork.join(', ') || 'None yet'}
Remaining work: ${remainingWork.join(', ')}

${completedWork.length > 0 ? `Previous contributions:\n${Object.entries(state.results || {})
  .filter(([role]) => role !== supervisorRole)
  .map(([role, result]) => `**${role}:** ${result}`)
  .join('\n\n')}` : ''}

Decide which team member should work next. Respond with just the role name: ${remainingWork.join(' or ')}.`;
      }

      try {
        const result = await agent.agent.invoke({
          messages: [...state.messages, new HumanMessage(supervisorPrompt)]
        }, config);

        const response = result.messages[result.messages.length - 1].content;

        // Notify progress callback for synthesis completion
        if (remainingWork.length === 0) {
          this.progressCallback?.onSupervisorSynthesisComplete?.(response);
        }

        return {
          messages: [new AIMessage({ content: response, name: supervisorRole })],
          currentRole: supervisorRole,
          next: remainingWork.length === 0 ? END : this.parseNextRole(response, remainingWork),
          results: {
            ...state.results,
            [supervisorRole]: response,
            // If this is the final synthesis, store it as the final result
            ...(remainingWork.length === 0 ? { final: response } : {})
          }
        };

      } catch (error) {
        console.error(`[RouKey Orchestrator] ❌ Supervisor failed:`, error);
        return {
          messages: [new AIMessage({
            content: `Supervisor error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            name: supervisorRole
          })],
          currentRole: supervisorRole,
          next: END
        };
      }
    };
  }

  /**
   * Create supervisor router for conditional edges
   */
  private createSupervisorRouter(workerRoles: string[]) {
    return (state: OrchestrationState) => {
      const next = state.next;
      if (next === END || !next) {
        return END;
      }

      // Validate that the next role is valid
      if (workerRoles.includes(next)) {
        return next;
      }

      // Fallback to END if invalid
      console.warn(`[RouKey Orchestrator] Invalid next role: ${next}, ending workflow`);
      return END;
    };
  }

  /**
   * Parse the next role from supervisor response
   */
  private parseNextRole(response: string, availableRoles: string[]): string {
    const lowerResponse = response.toLowerCase();

    // Look for exact role matches
    for (const role of availableRoles) {
      if (lowerResponse.includes(role.toLowerCase())) {
        return role;
      }
    }

    // If no match found, return the first available role
    return availableRoles[0] || END;
  }

  /**
   * Execute the workflow
   */
  private async executeWorkflow(): Promise<{
    finalResponse: string;
    roleContributions: Record<string, string>;
    totalTokens: number;
    conversationHistory: BaseMessage[];
  }> {
    if (!this.workflow) {
      throw new Error('Workflow not built');
    }

    console.log(`[RouKey Orchestrator] 🚀 Executing workflow with ${this.config.roles.length} agents`);

    const initialState: OrchestrationState = {
      messages: [new HumanMessage(this.config.originalPrompt)],
      conversationId: this.config.conversationId,
      userId: this.config.userId,
      results: {},
      taskProgress: {},
      metadata: {}
    };

    let totalTokens = 0;
    const conversationHistory: BaseMessage[] = [];

    try {
      // Execute the workflow
      const stream = await this.workflow.stream(initialState, {
        recursionLimit: this.config.maxIterations || 10
      });

      let finalState: OrchestrationState = initialState;

      for await (const output of stream) {
        if (output && typeof output === 'object') {
          // LangGraph returns output keyed by node name, extract the actual state
          const outputKeys = Object.keys(output);
          const nodeKey = outputKeys[0];
          const nodeOutput = output[nodeKey];

          if (nodeOutput && typeof nodeOutput === 'object') {
            // Properly merge the node output into final state
            finalState = {
              ...finalState,
              ...nodeOutput,
              results: {
                ...finalState.results,
                ...nodeOutput.results
              },
              taskProgress: {
                ...finalState.taskProgress,
                ...nodeOutput.taskProgress
              }
            };

            // Collect messages for conversation history
            if (nodeOutput.messages) {
              conversationHistory.push(...nodeOutput.messages);
            }

            // Log progress
            const currentRole = nodeKey;
            if (currentRole && currentRole !== '__end__') {
              console.log(`[RouKey Orchestrator] 📝 ${currentRole} step completed`);
            }
          }
        }
      }

      // Extract final response
      const results = finalState.results || {};
      let finalResponse: string;

      if (results.final) {
        finalResponse = results.final;
        console.log(`[RouKey Orchestrator] ✅ Using results.final (${finalResponse.length} chars)`);
      } else if (results.merger) {
        finalResponse = results.merger;
        console.log(`[RouKey Orchestrator] ✅ Using results.merger (${finalResponse.length} chars)`);
      } else {
        // Create a simple summary if no merger was used
        const roleContributions = Object.entries(results)
          .map(([role, result]) => `**${role.toUpperCase()}:**\n${result}`)
          .join('\n\n---\n\n');

        finalResponse = `# Multi-Agent Collaboration Results\n\n${roleContributions}`;
        console.log(`[RouKey Orchestrator] ⚠️ Using fallback summary (${finalResponse.length} chars)`);
      }

      console.log(`[RouKey Orchestrator] ✅ Workflow execution completed successfully`);

      return {
        finalResponse,
        roleContributions: results,
        totalTokens,
        conversationHistory
      };

    } catch (error) {
      console.error(`[RouKey Orchestrator] ❌ Workflow execution failed:`, error);
      throw error;
    }
  }
}
